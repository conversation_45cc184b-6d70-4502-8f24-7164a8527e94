from aws_cdk import (
    Duration,
    Stack,
    SecretValue,
    aws_ec2 as ec2,
    aws_rds as rds,
    aws_secretsmanager as secretsmanager,
    aws_ecs as ecs,
    aws_ecs_patterns as ecs_patterns,
    aws_logs as logs,
    aws_iam as iam,
    aws_ses as ses,
    aws_certificatemanager as acm,
    aws_route53 as route53,
    aws_route53_targets as targets,
    aws_elasticloadbalancingv2 as elbv2,
    aws_lambda as _lambda,
    aws_events as events,
    aws_events_targets as targets_events,
    aws_sqs as sqs,
    aws_ecr as ecr,
    aws_servicediscovery as servicediscovery,
    RemovalPolicy,
    CfnOutput,
)
from constructs import Construct


class ModernActionStack(Stack):
    def __init__(
        self, 
        scope: Construct, 
        construct_id: str,
        env_name: str,
        **kwargs
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)
        
        self.env_name = env_name
        
        # Create VPC with public and private subnets
        self.vpc = self._create_vpc()
        
        # Create RDS database
        self.database = self._create_database()
        
        # Create secrets for application configuration
        self.secrets = self._create_secrets()
        
        # Create ECR repositories for our containers
        self.ecr_repositories = self._create_ecr_repositories()
        
        # Create ECS cluster and services
        self.ecs_cluster = self._create_ecs_cluster()
        
        # Create IAM roles
        self.iam_roles = self._create_iam_roles()
        
        # Create Fargate services and task definitions
        self.fargate_services = self._create_fargate_services()

        # Create Lambda functions and background job infrastructure
        self.lambda_functions = self._create_lambda_functions()
        
        # Create SSL certificate for HTTPS (staging/prod only)
        self.certificate = self._create_certificate() if self.env_name != "dev" else None
        
        # Configure HTTPS listener if we have a certificate
        if self.certificate:
            self._configure_https_listener()
            
        # Create DNS records for staging/prod environments
        if self.env_name != "dev":
            self._create_dns_records()

        # Output important values
        self._create_outputs()

    def _create_vpc(self) -> ec2.Vpc:
        """Create VPC with public and private subnets"""
        return ec2.Vpc(
            self, 
            "ModernActionVPC",
            max_azs=2,
            nat_gateways=1,
            subnet_configuration=[
                ec2.SubnetConfiguration(
                    subnet_type=ec2.SubnetType.PUBLIC,
                    name="Public",
                    cidr_mask=24,
                ),
                ec2.SubnetConfiguration(
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                    name="Private",
                    cidr_mask=24,
                ),
            ],
            enable_dns_hostnames=True,
            enable_dns_support=True,
        )

    def _create_database(self) -> rds.DatabaseInstance:
        """Create RDS PostgreSQL database"""
        # Create security group for database
        db_security_group = ec2.SecurityGroup(
            self,
            "DatabaseSecurityGroup",
            vpc=self.vpc,
            description="Security group for ModernAction database",
            allow_all_outbound=False,
        )
        
        # Create database credentials in Secrets Manager
        db_credentials = rds.DatabaseSecret(
            self,
            "DatabaseCredentials",
            username="modernaction_admin",
        )
        
        # Create database subnet group
        db_subnet_group = rds.SubnetGroup(
            self,
            "DatabaseSubnetGroup",
            description="Subnet group for ModernAction database",
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
            ),
        )
        
        # Create database instance
        database = rds.DatabaseInstance(
            self,
            "ModernActionDatabase",
            engine=rds.DatabaseInstanceEngine.postgres(
                version=rds.PostgresEngineVersion.VER_15
            ),
            instance_type=ec2.InstanceType.of(
                ec2.InstanceClass.BURSTABLE3,
                ec2.InstanceSize.MICRO
            ),
            credentials=rds.Credentials.from_secret(db_credentials),
            database_name="modernaction",
            vpc=self.vpc,
            subnet_group=db_subnet_group,
            security_groups=[db_security_group],
            backup_retention=Duration.days(7),
            deletion_protection=self.env_name == "prod",
            delete_automated_backups=self.env_name != "prod",
            removal_policy=RemovalPolicy.DESTROY if self.env_name != "prod" else RemovalPolicy.RETAIN,
            storage_encrypted=True,
            multi_az=self.env_name == "prod",
            allocated_storage=20,
            max_allocated_storage=100,
            enable_performance_insights=True,
            performance_insight_retention=rds.PerformanceInsightRetention.DEFAULT,
            cloudwatch_logs_exports=["postgresql"],
            parameter_group=rds.ParameterGroup.from_parameter_group_name(
                self, "DatabaseParameterGroup", "default.postgres15"
            ),
        )
        
        return database

    def _create_secrets(self) -> dict:
        """Create secrets for application configuration"""
        secrets = {}
        
        # API Keys and external service credentials
        secrets["api_keys"] = secretsmanager.Secret(
            self,
            "ApiKeysSecret",
            description="API keys for external services (OpenStates, Google Civic Info, etc.)",
            secret_object_value={
                "OPENSTATES_API_KEY": SecretValue.unsafe_plain_text(""),
                "GOOGLE_CIVIC_INFO_API_KEY": SecretValue.unsafe_plain_text(""),
                "HUGGING_FACE_API_KEY": SecretValue.unsafe_plain_text(""),
            },
        )
        
        # JWT and session secrets
        secrets["jwt_secret"] = secretsmanager.Secret(
            self,
            "JwtSecret",
            description="JWT signing secret for authentication",
            generate_secret_string=secretsmanager.SecretStringGenerator(
                secret_string_template='{"jwt_secret": ""}',
                generate_string_key="jwt_secret",
                exclude_characters=" %+~`#$&*()|[]{}:;<>?!'/\"\\",
                password_length=64,
            ),
        )
        
        # Application configuration
        secrets["app_config"] = secretsmanager.Secret(
            self,
            "AppConfigSecret",
            description="Application configuration secrets",
            secret_object_value={
                "SECRET_KEY": SecretValue.unsafe_plain_text(""),
                "ENVIRONMENT": SecretValue.unsafe_plain_text(self.env_name),
                "CORS_ORIGINS": SecretValue.unsafe_plain_text("http://localhost:3000"),
            },
        )
        
        return secrets

    def _create_ecs_cluster(self) -> ecs.Cluster:
        """Create ECS cluster for running the application"""
        cluster = ecs.Cluster(
            self,
            "ModernActionCluster",
            vpc=self.vpc,
            cluster_name=f"modernaction-{self.env_name}",
            execute_command_configuration=ecs.ExecuteCommandConfiguration(
                logging=ecs.ExecuteCommandLogging.DEFAULT
            ),
        )
        
        return cluster

    def _create_iam_roles(self) -> dict:
        """Create IAM roles for different services"""
        roles = {}
        
        # Task execution role
        roles["task_execution_role"] = iam.Role(
            self,
            "TaskExecutionRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AmazonECSTaskExecutionRolePolicy"
                )
            ],
        )
        
        # Task role for application
        roles["task_role"] = iam.Role(
            self,
            "TaskRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
        )
        
        # Grant access to secrets
        for secret in self.secrets.values():
            secret.grant_read(roles["task_execution_role"])
            secret.grant_read(roles["task_role"])
        
        # Grant access to database secret
        self.database.secret.grant_read(roles["task_execution_role"])
        self.database.secret.grant_read(roles["task_role"])
        
        # Grant SES permissions
        roles["task_role"].add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendRawEmail",
                    "ses:GetSendQuota",
                    "ses:GetSendStatistics",
                ],
                resources=["*"],
            )
        )
        
        return roles

    def _create_ecr_repositories(self) -> dict:
        """Reference existing ECR repositories for our three container images"""
        repositories = {}
        
        # Reference existing API Service Repository
        repositories["api"] = ecr.Repository.from_repository_name(
            self,
            "ApiRepository",
            repository_name=f"modernaction-api-{self.env_name}"
        )
        
        # Reference existing Web Frontend Repository
        repositories["web"] = ecr.Repository.from_repository_name(
            self,
            "WebRepository", 
            repository_name=f"modernaction-web-{self.env_name}"
        )
        
        # Reference existing AI Worker Repository
        repositories["worker"] = ecr.Repository.from_repository_name(
            self,
            "WorkerRepository",
            repository_name=f"modernaction-worker-{self.env_name}"
        )
        
        return repositories

    def _create_fargate_services(self) -> dict:
        """Create Fargate services and task definitions"""
        services = {}
        
        # API Service (FastAPI backend) - Let this pattern create the ALB
        api_service = ecs_patterns.ApplicationLoadBalancedFargateService(
            self,
            "ApiService",
            cluster=self.ecs_cluster,
            cpu=256,  # Lightweight API service
            memory_limit_mib=512,
            desired_count=2 if self.env_name == "prod" else 1,
            task_image_options=ecs_patterns.ApplicationLoadBalancedTaskImageOptions(
                image=ecs.ContainerImage.from_ecr_repository(self.ecr_repositories["api"], "latest-amd64"),
                container_port=8000,
                task_role=self.iam_roles["task_role"],
                execution_role=self.iam_roles["task_execution_role"],
                secrets={
                    "SECRET_KEY": ecs.Secret.from_secrets_manager(self.secrets["jwt_secret"], "jwt_secret"),
                    "DB_USERNAME": ecs.Secret.from_secrets_manager(self.database.secret, "username"),
                    "DB_PASSWORD": ecs.Secret.from_secrets_manager(self.database.secret, "password"),
                },
                environment={
                    "ENVIRONMENT": self.env_name,
                    "DB_HOST": self.database.instance_endpoint.hostname,
                    "DB_PORT": str(self.database.instance_endpoint.port),
                    "DB_NAME": "modernaction",
                },
                log_driver=ecs.LogDrivers.aws_logs(
                    stream_prefix="api",
                    log_retention=logs.RetentionDays.ONE_MONTH,
                ),
            ),
            domain_zone=None,  # Will be configured later with custom domain
            listener_port=80,
            public_load_balancer=True,
            service_name=f"modernaction-api-{self.env_name}",
            # CRITICAL FIX: Add service discovery for internal communication
            cloud_map_options=ecs.CloudMapOptions(
                name="api",  # This creates internal DNS: api.{env_name}.local
                cloud_map_namespace=ecs.CloudMapNamespaceOptions(
                    name=f"{self.env_name}.local",
                    type=servicediscovery.NamespaceType.DNS_PRIVATE,
                    vpc=self.vpc
                )
            ),
        )
        
        # Get the ALB created by the API service
        alb = api_service.load_balancer
        
        # Configure health check for API service
        api_service.target_group.configure_health_check(
            path="/api/v1/health",
            healthy_http_codes="200",
            timeout=Duration.seconds(30),
            interval=Duration.seconds(60),
            healthy_threshold_count=2,
            unhealthy_threshold_count=5,
        )
        
        # Web Frontend Service (Next.js) - Using the same ALB as API
        web_task_definition = ecs.FargateTaskDefinition(
            self,
            "WebTaskDefinition",
            cpu=256,  # Lightweight web service
            memory_limit_mib=512,
            task_role=self.iam_roles["task_role"],
            execution_role=self.iam_roles["task_execution_role"],
        )
        
        # Determine the public API URL based on environment and certificate availability
        if self.env_name != "dev" and self.certificate:
            # Use HTTPS with custom domain for staging/prod
            public_api_url = f"https://{self.env_name}.modernaction.io/api/v1"
        else:
            # Use HTTP with ALB DNS name for dev or when no certificate
            public_api_url = f"http://{alb.load_balancer_dns_name}/api/v1"

        web_container = web_task_definition.add_container(
            "WebContainer",
            image=ecs.ContainerImage.from_ecr_repository(self.ecr_repositories["web"], "latest-amd64"),
            environment={
                # CRITICAL FIX: Add internal API URL for server-side requests
                "INTERNAL_API_URL": f"http://api.{self.env_name}.local:8000/api/v1",
                # Public API URL for client-side requests (browser)
                "NEXT_PUBLIC_API_URL": public_api_url,
                "NODE_ENV": "production",
            },
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="web",
                log_retention=logs.RetentionDays.ONE_MONTH,
            ),
        )
        
        web_container.add_port_mappings(
            ecs.PortMapping(
                container_port=3000,
                protocol=ecs.Protocol.TCP
            )
        )
        
        # Create Fargate service for web
        web_service = ecs.FargateService(
            self,
            "WebService",
            cluster=self.ecs_cluster,
            task_definition=web_task_definition,
            desired_count=2 if self.env_name == "prod" else 1,
            service_name=f"modernaction-web-{self.env_name}",
        )
        
        # Create target group for web service on port 3000
        web_target_group = elbv2.ApplicationTargetGroup(
            self,
            "WebTargetGroup",
            port=3000,
            protocol=elbv2.ApplicationProtocol.HTTP,
            vpc=self.vpc,
            targets=[web_service],
            health_check=elbv2.HealthCheck(
                path="/",
                healthy_http_codes="200",
                timeout=Duration.seconds(30),
                interval=Duration.seconds(60),
                healthy_threshold_count=2,
                unhealthy_threshold_count=5,
            ),
        )
        
        # Configure path-based routing on the existing ALB
        # Get the default listener (port 80) that was created by the API service pattern
        default_listener = api_service.listener
        
        # Add rule to route /api/* traffic to API service (existing target group)
        elbv2.ApplicationListenerRule(
            self,
            "ApiRoutingRule",
            listener=default_listener,
            priority=100,
            conditions=[
                elbv2.ListenerCondition.path_patterns(["/api/*"])
            ],
            action=elbv2.ListenerAction.forward([api_service.target_group])
        )
        
        # Modify the default action to route all other traffic to web service
        # We need to add a catch-all rule with lower priority than the API rule
        elbv2.ApplicationListenerRule(
            self,
            "WebDefaultRule",
            listener=default_listener, 
            priority=200,
            conditions=[
                elbv2.ListenerCondition.path_patterns(["/*"])
            ],
            action=elbv2.ListenerAction.forward([web_target_group])
        )
        
        # Health check is configured in the target group above
        
        # AI Worker Task Definition (for scheduled/one-off tasks)
        worker_task_definition = ecs.FargateTaskDefinition(
            self,
            "WorkerTaskDefinition",
            cpu=1024,  # More CPU for ML processing
            memory_limit_mib=2048,  # More memory for ML models
            task_role=self.iam_roles["task_role"],
            execution_role=self.iam_roles["task_execution_role"],
        )
        
        worker_container = worker_task_definition.add_container(
            "WorkerContainer",
            image=ecs.ContainerImage.from_registry("alpine:latest"),
            secrets={
                "SECRET_KEY": ecs.Secret.from_secrets_manager(self.secrets["jwt_secret"], "jwt_secret"),
                "OPENSTATES_API_KEY": ecs.Secret.from_secrets_manager(self.secrets["api_keys"], "OPENSTATES_API_KEY"),
                "HUGGING_FACE_API_KEY": ecs.Secret.from_secrets_manager(self.secrets["api_keys"], "HUGGING_FACE_API_KEY"),
                "DB_USERNAME": ecs.Secret.from_secrets_manager(self.database.secret, "username"),
                "DB_PASSWORD": ecs.Secret.from_secrets_manager(self.database.secret, "password"),
            },
            environment={
                "ENVIRONMENT": self.env_name,
                "DB_HOST": self.database.instance_endpoint.hostname,
                "DB_PORT": str(self.database.instance_endpoint.port), 
                "DB_NAME": "modernaction",
            },
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="worker",
                log_retention=logs.RetentionDays.ONE_MONTH,
            ),
        )
        
        # Allow ALB to connect to web service on port 3000
        alb.connections.allow_to(
            web_service,
            ec2.Port.tcp(3000),
            "Allow ALB to connect to Web service"
        )

        # CRITICAL FIX: Allow web service to connect directly to API service
        # This enables server-side requests from Next.js to the FastAPI backend
        api_service.service.connections.allow_from(
            web_service,
            ec2.Port.tcp(8000),
            "Allow Web service to connect to API service for server-side requests"
        )

        # Allow Fargate services to connect to database
        self.database.connections.allow_from(
            api_service.service,
            ec2.Port.tcp(5432),
            "Allow API service to connect to RDS"
        )

        self.database.connections.allow_from(
            web_service,
            ec2.Port.tcp(5432),
            "Allow Web service to connect to RDS (if needed)"
        )
        
        # Note: Worker tasks will run in the same security groups as the API service
        # so they inherit the database connection permissions
        
        services["api"] = api_service
        services["web"] = web_service
        services["web_target_group"] = web_target_group
        services["worker_task_definition"] = worker_task_definition
        services["load_balancer"] = alb
        
        return services

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs"""
        # Lambda function outputs
        CfnOutput(
            self,
            "BillStatusUpdateLambdaArn",
            value=self.lambda_functions["bill_status_update"].function_arn,
            description="Bill Status Update Lambda Function ARN",
        )

        CfnOutput(
            self,
            "NotificationSenderLambdaArn",
            value=self.lambda_functions["notification_sender"].function_arn,
            description="Notification Sender Lambda Function ARN",
        )

        CfnOutput(
            self,
            "BillStatusQueueUrl",
            value=self.lambda_functions["bill_status_queue"].queue_url,
            description="Bill Status Notification Queue URL",
        )

        CfnOutput(
            self,
            "VpcId",
            value=self.vpc.vpc_id,
            description="VPC ID",
        )
        
        CfnOutput(
            self,
            "DatabaseEndpoint",
            value=self.database.instance_endpoint.hostname,
            description="Database endpoint",
        )
        
        CfnOutput(
            self,
            "DatabasePort",
            value=str(self.database.instance_endpoint.port),
            description="Database port",
        )
        
        CfnOutput(
            self,
            "DatabaseSecretArn",
            value=self.database.secret.secret_arn,
            description="Database secret ARN",
        )
        
        CfnOutput(
            self,
            "EcsClusterArn",
            value=self.ecs_cluster.cluster_arn,
            description="ECS Cluster ARN",
        )
        
        # ECR Repository outputs
        for name, repo in self.ecr_repositories.items():
            CfnOutput(
                self,
                f"{name.title()}EcrRepositoryUri",
                value=repo.repository_uri,
                description=f"{name.title()} ECR repository URI",
            )
        
        # Fargate Service outputs
        CfnOutput(
            self,
            "ApiServiceUrl",
            value=f"http://{self.fargate_services['api'].load_balancer.load_balancer_dns_name}",
            description="API Service Load Balancer URL",
        )
        
        CfnOutput(
            self,
            "WebServiceUrl", 
            value=f"http://{self.fargate_services['load_balancer'].load_balancer_dns_name}",
            description="Web Service Load Balancer URL (default route)",
        )
        
        CfnOutput(
            self,
            "LoadBalancerDnsName",
            value=self.fargate_services["load_balancer"].load_balancer_dns_name,
            description="Application Load Balancer DNS name",
        )
        
        CfnOutput(
            self,
            "WorkerTaskDefinitionArn",
            value=self.fargate_services["worker_task_definition"].task_definition_arn,
            description="Worker Task Definition ARN for running one-off tasks",
        )
        
        for name, secret in self.secrets.items():
            CfnOutput(
                self,
                f"{name.title()}SecretArn",
                value=secret.secret_arn,
                description=f"{name.title()} secret ARN",
            )

    def _create_lambda_functions(self) -> dict:
        """Create Lambda functions for background jobs"""
        lambda_functions = {}

        # Create SQS queue for bill status change notifications
        bill_status_queue = sqs.Queue(
            self,
            "BillStatusNotificationQueue",
            queue_name=f"modernaction-bill-status-notifications-{self.env_name}",
            visibility_timeout=Duration.minutes(15),  # Match Lambda timeout
            retention_period=Duration.days(14),
            dead_letter_queue=sqs.DeadLetterQueue(
                max_receive_count=3,
                queue=sqs.Queue(
                    self,
                    "BillStatusNotificationDLQ",
                    queue_name=f"modernaction-bill-status-notifications-dlq-{self.env_name}",
                    retention_period=Duration.days(14)
                )
            )
        )

        # Create Lambda execution role with necessary permissions
        lambda_execution_role = iam.Role(
            self,
            "BillStatusLambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSLambdaBasicExecutionRole"),
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSLambdaVPCAccessExecutionRole")
            ]
        )

        # Grant permissions to access secrets
        for secret in self.secrets.values():
            secret.grant_read(lambda_execution_role)

        # Grant permissions to access RDS
        self.database.grant_connect(lambda_execution_role)

        # Grant permissions to send messages to SQS
        bill_status_queue.grant_send_messages(lambda_execution_role)
        
        # Grant permissions to receive messages from SQS (for notification sender)
        bill_status_queue.grant_consume_messages(lambda_execution_role)
        
        # Grant permissions to send emails via SES
        lambda_execution_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendRawEmail",
                    "ses:GetSendQuota",
                    "ses:GetSendStatistics",
                ],
                resources=["*"],
            )
        )

        # Create security group for Lambda functions
        lambda_security_group = ec2.SecurityGroup(
            self,
            "LambdaSecurityGroup",
            vpc=self.vpc,
            description="Security group for Lambda functions",
            allow_all_outbound=True
        )

        # Allow Lambda to connect to RDS
        self.database.connections.allow_from(
            lambda_security_group,
            ec2.Port.tcp(5432),
            "Allow Lambda to connect to RDS"
        )

        # Bill Status Update Lambda Function
        bill_status_lambda = _lambda.Function(
            self,
            "BillStatusUpdateFunction",
            function_name=f"modernaction-bill-status-update-{self.env_name}",
            runtime=_lambda.Runtime.PYTHON_3_11,
            handler="handler.lambda_handler",
            code=_lambda.Code.from_asset("../apps/lambda/bill_status_update"),
            timeout=Duration.minutes(15),
            memory_size=512,
            role=lambda_execution_role,
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
            security_groups=[lambda_security_group],
            environment={
                "OPEN_STATES_API_KEY": self.secrets["api_keys"].secret_value_from_json("OPENSTATES_API_KEY").unsafe_unwrap(),
                "DB_HOST": self.database.instance_endpoint.hostname,
                "DB_PORT": str(self.database.instance_endpoint.port),
                "DB_NAME": "modernaction",
                "DB_USER": self.database.secret.secret_value_from_json("username").unsafe_unwrap(),
                "DB_PASSWORD": self.database.secret.secret_value_from_json("password").unsafe_unwrap(),
                "SQS_QUEUE_URL": bill_status_queue.queue_url
            },
            log_retention=logs.RetentionDays.ONE_MONTH
        )

        # Schedule the bill status update function to run daily
        bill_status_schedule = events.Rule(
            self,
            "BillStatusUpdateSchedule",
            rule_name=f"modernaction-bill-status-update-schedule-{self.env_name}",
            description="Daily trigger for bill status updates",
            schedule=events.Schedule.rate(Duration.days(1))
        )

        bill_status_schedule.add_target(
            targets_events.LambdaFunction(bill_status_lambda)
        )

        # Notification Sender Lambda Function
        notification_sender_lambda = _lambda.Function(
            self,
            "NotificationSenderFunction",
            function_name=f"modernaction-notification-sender-{self.env_name}",
            runtime=_lambda.Runtime.PYTHON_3_11,
            handler="handler.lambda_handler",
            code=_lambda.Code.from_asset("../apps/lambda/notification_sender"),
            timeout=Duration.minutes(15),
            memory_size=512,
            role=lambda_execution_role,
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
            security_groups=[lambda_security_group],
            environment={
                "DB_HOST": self.database.instance_endpoint.hostname,
                "DB_PORT": str(self.database.instance_endpoint.port),
                "DB_NAME": "modernaction",
                "DB_USER": self.database.secret.secret_value_from_json("username").unsafe_unwrap(),
                "DB_PASSWORD": self.database.secret.secret_value_from_json("password").unsafe_unwrap(),
                "FROM_EMAIL": "<EMAIL>",
                "REPLY_TO_EMAIL": "<EMAIL>"
            },
            log_retention=logs.RetentionDays.ONE_MONTH
        )

        # Configure SQS to trigger the notification sender Lambda
        from aws_cdk import aws_lambda_event_sources as lambda_events
        
        notification_sender_lambda.add_event_source(
            lambda_events.SqsEventSource(
                queue=bill_status_queue,
                batch_size=10,  # Process up to 10 messages at once
                max_batching_window=Duration.seconds(30)  # Wait up to 30 seconds to batch messages
            )
        )

        lambda_functions["bill_status_update"] = bill_status_lambda
        lambda_functions["notification_sender"] = notification_sender_lambda
        lambda_functions["bill_status_queue"] = bill_status_queue

        return lambda_functions

    def _create_certificate(self) -> acm.Certificate:
        """Create SSL certificate for the domain"""
        # For staging environment, we need both staging.modernaction.io and *.staging.modernaction.io
        # For production, we'll use both modernaction.io and *.modernaction.io
        if self.env_name == "staging":
            domain_name = "staging.modernaction.io"
            subject_alternative_names = ["*.staging.modernaction.io"]
        else:
            domain_name = "modernaction.io"
            subject_alternative_names = ["*.modernaction.io"]
        
        # Note: This assumes you have a hosted zone for modernaction.io in Route 53
        # If you don't have this, you'll need to create it manually first
        hosted_zone = route53.HostedZone.from_lookup(
            self,
            "HostedZone",
            domain_name="modernaction.io"
        )
        
        certificate = acm.Certificate(
            self,
            "SSLCertificate",
            domain_name=domain_name,
            subject_alternative_names=subject_alternative_names,
            validation=acm.CertificateValidation.from_dns(hosted_zone),
        )
        
        return certificate
    
    def _configure_https_listener(self) -> None:
        """Configure HTTPS listener with SSL certificate"""
        alb = self.fargate_services["load_balancer"]
        
        # Add HTTPS listener
        https_listener = alb.add_listener(
            "HttpsListener",
            port=443,
            protocol=elbv2.ApplicationProtocol.HTTPS,
            certificates=[self.certificate],
            default_action=elbv2.ListenerAction.forward([
                # Default to web service target group
                self.fargate_services["web_target_group"]
            ])
        )
        
        # Add API routing rule for HTTPS
        elbv2.ApplicationListenerRule(
            self,
            "HttpsApiRoutingRule",
            listener=https_listener,
            priority=100,
            conditions=[
                elbv2.ListenerCondition.path_patterns(["/api/*"])
            ],
            action=elbv2.ListenerAction.forward([self.fargate_services["api"].target_group])
        )
        
        # Modify HTTP listener to redirect to HTTPS
        http_listener = self.fargate_services["api"].listener
        
        # Remove existing rules and set redirect as default
        cfn_listener = http_listener.node.default_child
        cfn_listener.default_actions = [
            {
                "type": "redirect",
                "redirectConfig": {
                    "protocol": "HTTPS",
                    "port": "443",
                    "statusCode": "HTTP_301"
                }
            }
        ]
    
    def _create_dns_records(self) -> None:
        """Create Route 53 DNS records pointing to the load balancer"""
        hosted_zone = route53.HostedZone.from_lookup(
            self,
            "HostedZoneForDNS",
            domain_name="modernaction.io"
        )
        
        # Create the subdomain for this environment
        subdomain = f"api.{self.env_name}.modernaction.io" if self.env_name == "staging" else "api.modernaction.io"
        web_subdomain = f"{self.env_name}.modernaction.io" if self.env_name == "staging" else "modernaction.io"
        
        alb = self.fargate_services["load_balancer"]
        
        # API subdomain (api.staging.modernaction.io or api.modernaction.io)
        route53.ARecord(
            self,
            "ApiDnsRecord",
            zone=hosted_zone,
            record_name=subdomain,
            target=route53.RecordTarget.from_alias(
                targets.LoadBalancerTarget(alb)
            )
        )
        
        # Web/main subdomain (staging.modernaction.io or modernaction.io)
        route53.ARecord(
            self,
            "WebDnsRecord", 
            zone=hosted_zone,
            record_name=web_subdomain,
            target=route53.RecordTarget.from_alias(
                targets.LoadBalancerTarget(alb)
            )
        )