{"app": "python app.py", "watch": {"include": ["**"], "exclude": ["README.md", "cdk*.json", "requirements*.txt", "source.bat", "**/__pycache__", "**/.venv"]}, "context": {"@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": true, "@aws-cdk/core:stackRelativeExports": true, "@aws-cdk/aws-rds:lowercaseDbIdentifier": true, "@aws-cdk/aws-lambda:recognizeVersionProps": true, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": true, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": true, "@aws-cdk/aws-kms:defaultKeyPolicies": true, "@aws-cdk/aws-s3:grantWriteWithoutAcl": true, "@aws-cdk/aws-rds:auroraClusterChangeSizeToNotAllowed": true, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": true, "@aws-cdk/aws-lambda:automaticAsyncInvocation": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": true, "@aws-cdk/aws-iam:standardizedServicePrincipals": true, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": true, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": true, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": true, "@aws-cdk/aws-route53-patters:useCertificate": true, "@aws-cdk/customresources:installLatestAwsSdkDefault": false, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": true, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": true, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": true, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": true, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": true, "@aws-cdk/aws-redshift:columnId": true, "@aws-cdk/aws-stepfunctions-tasks:enableLogging": true, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": true, "@aws-cdk/aws-kms:aliasNameRef": true, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": true, "@aws-cdk/core:includePrefixInUniqueNameGeneration": true, "@aws-cdk/aws-efs:denyAnonymousAccess": true, "@aws-cdk/aws-opensearchservice:enableLogging": true, "@aws-cdk/aws-lambda:useLatestRuntimeVersion": true, "@aws-cdk/aws-lambda:codelessEventSourceMapping": true, "@aws-cdk/aws-iam:externalIdCondition": true, "@aws-cdk/aws-rds:snapshotIdentifierPrefix": true, "@aws-cdk/aws-s3:eventBridgeNotification": true, "@aws-cdk/aws-s3:allowedLegacyPolicyGeneration": true, "@aws-cdk/aws-apigateway:useAgentlessForApiKeys": true, "@aws-cdk/aws-ec2:prefixInterfaceEndpointServiceName": true, "@aws-cdk/aws-route53:recordSetNameValidation": true, "@aws-cdk/aws-route53:domainNameLabelValidation": true, "@aws-cdk/aws-lambda:warningForUnsupportedRuntime": true, "@aws-cdk/aws-cloudwatch:useMetricFiltersV2": true, "@aws-cdk/aws-sns:restrictSqsDescryption": true, "@aws-cdk/aws-elb:elbTargetGroupPortFromPort": true}}