{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(poetry run alembic:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(poetry install:*)", "<PERSON><PERSON>(poetry lock:*)", "<PERSON><PERSON>(poetry run pytest:*)", "<PERSON><PERSON>(poetry show:*)", "<PERSON><PERSON>(poetry add:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(poetry run python:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx storybook:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pip install:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "Bash(npx:*)", "Bash(docker build:*)", "<PERSON><PERSON>(timeout 60s docker build:*)", "Bash(export AWS_ACCOUNT_ID=************)", "Bash(export AWS_REGION=us-east-1)", "Bash(export ENVIRONMENT=dev)", "<PERSON><PERSON>(echo:*)", "Bash(docker tag:*)", "<PERSON><PERSON>(docker push:*)", "Bash(export CDK_DEFAULT_ACCOUNT=************)", "Bash(export:*)", "Bash(cdk deploy:*)", "Bash(cdk:*)", "<PERSON><PERSON>(source:*)", "Bash(aws ecr:*)", "Bash(ls:*)", "Bash(aws configure:*)", "Bash(aws sts:*)", "Bash(aws cloudformation list-stacks:*)", "Bash(aws ecs:*)", "Bash(aws elbv2 describe-load-balancers:*)", "Bash(aws route53 list-hosted-zones:*)", "Bash(aws acm list-certificates:*)", "Bash(aws iam list-attached-user-policies:*)", "Bash(aws ec2 describe-vpcs:*)", "Bash(aws ec2 describe-subnets:*)", "Bash(aws elbv2 describe-target-groups:*)", "<PERSON><PERSON>(aws iam get-role:*)", "<PERSON><PERSON>(aws iam create-role:*)", "<PERSON><PERSON>(aws iam attach-role-policy:*)", "Bash(aws logs create-log-group:*)", "Bash(aws ec2 describe-security-groups:*)", "Bash(--cluster modernaction-staging )", "Bash(--service-name modernaction-api-dev )", "Bash(--task-definition modernaction-api-dev:1 )", "Bash(--desired-count 1 )", "Bash(--launch-type FARGATE )", "Bash(--network-configuration \"awsvpcConfiguration={subnets=[subnet-05876aee8f30a825e,subnet-0b1f8baca2a86e0cc],securityGroups=[sg-009b7b0348910eb13],assignPublicIp=ENABLED}\" )", "Bash(--load-balancers targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/staging-target/4fe70d60f42da1bd,containerName=modernaction-api,containerPort=8000)", "Bash(aws acm describe-certificate:*)", "Bash(aws elbv2 describe-target-health:*)", "Bash(aws logs get-log-events:*)", "Bash(aws ec2 describe-network-interfaces:*)", "Bash(aws ec2 authorize-security-group-ingress:*)", "Bash(ENVIRONMENT=staging cdk bootstrap)", "Bash(aws cloudformation delete-stack:*)", "Bash(aws cloudformation wait:*)", "Bash(aws cloudformation:*)", "Bash(aws ssm delete-parameter:*)", "Bash(ENVIRONMENT=staging cdk bootstrap --force)", "Bash(ENVIRONMENT=staging cdk bootstrap --qualifier modern2025)", "Bash(ENVIRONMENT=staging cdk deploy --no-bootstrap)", "Bash(aws s3api create-bucket:*)", "Bash(aws rds:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(docker:*)", "Bash(ENVIRONMENT=staging cdk deploy --require-approval never)", "Bash(timeout 300 aws cloudformation wait stack-create-complete --stack-name modernaction-staging)", "Bash(aws logs:*)", "Ba<PERSON>(aws secretsmanager get-secret-value:*)", "Bash(git restore:*)", "Bash(aws route53:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(aws elbv2 describe-listeners:*)", "Bash(aws elbv2 describe-rules:*)", "Bash(brew install:*)", "<PERSON><PERSON>(k6 run:*)", "Bash(npm audit:*)", "<PERSON><PERSON>(poetry check:*)", "<PERSON><PERSON>(safety check:*)", "<PERSON><PERSON>(aws iam list-roles:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(git push:*)"], "deny": []}}