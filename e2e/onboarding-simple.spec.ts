/**
 * Simplified End-to-End Tests for User Onboarding Flow
 * 
 * This test suite validates the core onboarding functionality
 * with practical, working tests that avoid localStorage security issues.
 */

import { test, expect } from '@playwright/test';

test.describe('User Onboarding Flow - Core Functionality', () => {
  
  test('First-time user sees onboarding modal and can complete it', async ({ page }) => {
    console.log('🧪 Testing complete onboarding flow...');
    
    // Navigate to homepage (this will trigger onboarding for new users)
    await page.goto('/');
    
    // Wait for page to load and React to initialize
    await page.waitForTimeout(2000);
    
    // Clear localStorage to simulate first-time user
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (error) {
        console.log('LocalStorage not available:', error);
      }
    });
    
    // Reload page to trigger onboarding
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Verify onboarding modal appears
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    console.log('✅ Onboarding modal appeared for first-time user');
    
    // Verify welcome content
    await expect(page.getByText('Welcome to ModernAction')).toBeVisible();
    
    // Select some issues
    const environmentButton = page.getByTestId('issue-environment');
    const healthcareButton = page.getByTestId('issue-healthcare');
    
    await expect(environmentButton).toBeVisible();
    await expect(healthcareButton).toBeVisible();
    
    await environmentButton.click();
    await healthcareButton.click();
    
    console.log('✅ Issues selected successfully');
    
    // Save preferences
    const saveButton = page.getByTestId('onboarding-save-button');
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    
    // Wait for modal to disappear
    await page.waitForTimeout(1000);
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Onboarding completed and modal dismissed');
    
    // Verify localStorage was updated (if accessible)
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    if (localStorageData && !localStorageData.error) {
      console.log('✅ localStorage updated:', localStorageData);
    } else {
      console.log('ℹ️ localStorage not accessible in test environment');
    }
  });

  test('Skip onboarding functionality works', async ({ page }) => {
    console.log('🧪 Testing skip onboarding...');
    
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    // Clear localStorage first
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {
        console.log('LocalStorage clear failed');
      }
    });
    
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Verify modal appears
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    // Click skip button
    const skipButton = page.getByTestId('onboarding-skip-button');
    await expect(skipButton).toBeVisible();
    await skipButton.click();
    
    // Verify modal disappears
    await page.waitForTimeout(1000);
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Skip onboarding functionality works');
  });

  test('Issue selection toggles correctly', async ({ page }) => {
    console.log('🧪 Testing issue selection behavior...');
    
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    // Clear localStorage to ensure modal appears
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {}
    });
    
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Wait for modal
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    const environmentButton = page.getByTestId('issue-environment');
    
    // Initially should not be selected
    await expect(environmentButton).not.toHaveClass(/border-blue-500/);
    
    // Click to select
    await environmentButton.click();
    await expect(environmentButton).toHaveClass(/border-blue-500/);
    
    // Click again to deselect
    await environmentButton.click();
    await expect(environmentButton).not.toHaveClass(/border-blue-500/);
    
    console.log('✅ Issue selection toggle works correctly');
  });

  test('Modal has proper accessibility attributes', async ({ page }) => {
    console.log('🧪 Testing accessibility...');
    
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    // Clear localStorage to ensure modal appears
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {}
    });
    
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Check for dialog role
    const dialog = page.locator('[role="dialog"]');
    await expect(dialog).toBeVisible();
    
    console.log('✅ Modal has proper accessibility attributes');
  });

  test('All issue categories are displayed', async ({ page }) => {
    console.log('🧪 Testing issue categories display...');
    
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    // Clear localStorage
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {}
    });
    
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Check that multiple issue buttons are present
    const issueButtons = await page.locator('[data-testid^="issue-"]').count();
    expect(issueButtons).toBeGreaterThan(5); // Should have many issue categories
    
    // Verify some specific issues
    await expect(page.getByTestId('issue-environment')).toBeVisible();
    await expect(page.getByTestId('issue-healthcare')).toBeVisible();
    await expect(page.getByTestId('issue-education')).toBeVisible();
    
    console.log(`✅ Found ${issueButtons} issue categories`);
  });

});

test.describe('Onboarding Performance', () => {

  test('Modal appears within reasonable time', async ({ page }) => {
    console.log('🧪 Testing modal performance...');
    
    const startTime = Date.now();
    
    await page.goto('/');
    
    // Clear localStorage
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {}
    });
    
    await page.reload();
    
    // Wait for modal to appear
    await page.getByTestId('onboarding-modal').waitFor({ state: 'visible', timeout: 5000 });
    
    const renderTime = Date.now() - startTime;
    
    // Should render within 5 seconds (reasonable for E2E test)
    expect(renderTime).toBeLessThan(5000);
    
    console.log(`✅ Modal rendered in ${renderTime}ms`);
  });

});