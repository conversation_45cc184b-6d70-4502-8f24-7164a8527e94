// Centralized API client for ModernAction.io
// Uses axios for robust HTTP client features like interceptors and error handling

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  Campaign,
  CampaignCreate,
  CampaignUpdate,
  CampaignSearchParams,
  Bill,
  BillC<PERSON>,
  Bill<PERSON>p<PERSON>,
  BillSearchParams,
  Official,
  OfficialCreate,
  OfficialUpdate,
  OfficialSearchParams,
  Action,
  ApiError,
  PaginationParams
} from '../types';

// CRITICAL FIX: Use different URLs for server-side vs client-side requests
// Server-side requests (from Next.js server) should use internal service discovery
// Client-side requests (from browser) should use public ALB URL
const isServer = typeof window === 'undefined';
const apiUrl = isServer
  ? process.env.INTERNAL_API_URL || 'http://localhost:8000/api/v1'
  : process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

// Create axios instance with base configuration
// Increased timeout for internal service discovery DNS resolution
const apiClient: AxiosInstance = axios.create({
  baseURL: apiUrl,
  timeout: 30000, // 30 seconds for internal service discovery
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens (future use)
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token when available
    // const token = getAuthToken();
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access - redirecting to login');
      // Redirect to login page
    } else if (error.response?.status === 404) {
      // Handle not found errors
      console.error('Resource not found:', error.response.data);
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

// Helper function to build query string from parameters
const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });
  
  return searchParams.toString();
};

// Campaign API functions
export const campaignApi = {
  // Get all campaigns with pagination
  getCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/?${queryString}`);
    return response.data;
  },

  // Get single campaign by ID
  getCampaignById: async (id: string): Promise<Campaign> => {
    const response = await apiClient.get<Campaign>(`/campaigns/${id}`);
    return response.data;
  },

  // Create new campaign
  createCampaign: async (campaignData: CampaignCreate): Promise<Campaign> => {
    const response = await apiClient.post<Campaign>('/campaigns/', campaignData);
    return response.data;
  },

  // Update existing campaign
  updateCampaign: async (id: string, campaignData: CampaignUpdate): Promise<Campaign> => {
    const response = await apiClient.put<Campaign>(`/campaigns/${id}`, campaignData);
    return response.data;
  },

  // Delete campaign
  deleteCampaign: async (id: string): Promise<void> => {
    await apiClient.delete(`/campaigns/${id}`);
  },

  // Search campaigns with filters
  searchCampaigns: async (params: CampaignSearchParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/search?${queryString}`);
    return response.data;
  },

  // Get featured campaigns
  getFeaturedCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/featured?${queryString}`);
    return response.data;
  },

  // Get active campaigns
  getActiveCampaigns: async (params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/active?${queryString}`);
    return response.data;
  },

  // Get campaigns by status
  getCampaignsByStatus: async (status: string, params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/status/${status}?${queryString}`);
    return response.data;
  },

  // Get campaigns by bill ID
  getCampaignsByBill: async (billId: string, params: PaginationParams = {}): Promise<Campaign[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Campaign[]>(`/campaigns/bill/${billId}?${queryString}`);
    return response.data;
  },
};

// Bill API functions
export const billApi = {
  // Get all bills with pagination
  getBills: async (params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/?${queryString}`);
    return response.data;
  },

  // Get single bill by ID
  getBillById: async (id: string): Promise<Bill> => {
    const response = await apiClient.get<Bill>(`/bills/${id}`);
    return response.data;
  },

  // Create new bill
  createBill: async (billData: BillCreate): Promise<Bill> => {
    const response = await apiClient.post<Bill>('/bills/', billData);
    return response.data;
  },

  // Update existing bill
  updateBill: async (id: string, billData: BillUpdate): Promise<Bill> => {
    const response = await apiClient.put<Bill>(`/bills/${id}`, billData);
    return response.data;
  },

  // Delete bill
  deleteBill: async (id: string): Promise<void> => {
    await apiClient.delete(`/bills/${id}`);
  },

  // Search bills with filters
  searchBills: async (params: BillSearchParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/search?${queryString}`);
    return response.data;
  },

  // Get featured bills
  getFeaturedBills: async (params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/featured?${queryString}`);
    return response.data;
  },

  // Get bills by status
  getBillsByStatus: async (status: string, params: PaginationParams = {}): Promise<Bill[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Bill[]>(`/bills/status/${status}?${queryString}`);
    return response.data;
  },
};

// Official API functions
export const officialApi = {
  // Get all officials with pagination
  getOfficials: async (params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/?${queryString}`);
    return response.data;
  },

  // Get single official by ID
  getOfficialById: async (id: string): Promise<Official> => {
    const response = await apiClient.get<Official>(`/officials/${id}`);
    return response.data;
  },

  // Create new official
  createOfficial: async (officialData: OfficialCreate): Promise<Official> => {
    const response = await apiClient.post<Official>('/officials/', officialData);
    return response.data;
  },

  // Update existing official
  updateOfficial: async (id: string, officialData: OfficialUpdate): Promise<Official> => {
    const response = await apiClient.put<Official>(`/officials/${id}`, officialData);
    return response.data;
  },

  // Delete official
  deleteOfficial: async (id: string): Promise<void> => {
    await apiClient.delete(`/officials/${id}`);
  },

  // Search officials with filters
  searchOfficials: async (params: OfficialSearchParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/search?${queryString}`);
    return response.data;
  },

  // Get officials by zip code
  getOfficialsByZip: async (zipCode: string, params: PaginationParams = {}): Promise<Official[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Official[]>(`/officials/zip/${zipCode}?${queryString}`);
    return response.data;
  },
};

// Action API interface for creating actions
export interface ActionCreateRequest {
  subject: string;
  message: string;
  action_type?: string;
  user_name: string;
  user_email: string;
  user_address?: string;
  user_zip_code?: string;
  campaign_id: string;
  official_id: string;
  contact_email?: string;
  contact_phone?: string;
  contact_address?: string;
}

export interface ActionCreateResponse {
  id: string;
  subject: string;
  message: string;
  action_type: string;
  status: string;
  user_name: string;
  user_email: string;
  campaign_id: string;
  official_id: string;
  created_at: string;
  updated_at: string;
}

// Action API functions
export const actionApi = {
  // Create new action
  createAction: async (actionData: ActionCreateRequest): Promise<ActionCreateResponse> => {
    const response = await apiClient.post<ActionCreateResponse>('/actions/', actionData);
    return response.data;
  },

  // Get single action by ID
  getActionById: async (id: string): Promise<Action> => {
    const response = await apiClient.get<Action>(`/actions/${id}`);
    return response.data;
  },

  // Get actions with filters
  getActions: async (params: Record<string, any> = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/?${queryString}`);
    return response.data;
  },

  // Retry failed action
  retryAction: async (id: string): Promise<ActionCreateResponse> => {
    const response = await apiClient.post<ActionCreateResponse>(`/actions/${id}/retry`);
    return response.data;
  },

  // Delete action
  deleteAction: async (id: string): Promise<void> => {
    await apiClient.delete(`/actions/${id}`);
  },

  // Get action statistics
  getActionStats: async (campaignId?: string): Promise<any> => {
    const url = campaignId ? `/actions/stats?campaign_id=${campaignId}` : '/actions/stats';
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get user actions
  getUserActions: async (userId: string, params: PaginationParams = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/user/${userId}?${queryString}`);
    return response.data;
  },

  // Get campaign actions
  getCampaignActions: async (campaignId: string, params: PaginationParams = {}): Promise<Action[]> => {
    const queryString = buildQueryString(params);
    const response = await apiClient.get<Action[]>(`/actions/campaign/${campaignId}/stats?${queryString}`);
    return response.data;
  },
};

// Export the configured axios instance for custom requests
export default apiClient;
