import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import ActionModal from './ActionModal';
import { Campaign, Official, CampaignStatus, CampaignType, OfficialLevel, Chamber } from '../../types';

// Mock bill for campaign
const mockBill = {
  id: 'bill-1',
  title: 'Environmental Protection Act',
  description: 'A bill to protect the environment',
  bill_number: 'HR-123',
  bill_type: 'house_bill',
  status: 'introduced',
  session_year: 2024,
  chamber: 'house',
  state: 'federal',
  is_featured: false,
  priority_score: 5,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock data
const mockCampaign: Campaign = {
  id: 'campaign-1',
  title: 'Test Campaign',
  description: 'This is a test campaign for environmental protection',
  campaign_type: CampaignType.SUPPORT,
  status: CampaignStatus.ACTIVE,
  call_to_action: 'Please support environmental protection legislation.',
  bill_id: 'bill-1',
  bill: mockBill,
  target_actions: 1000,
  actual_actions: 250,
  is_featured: false,
  is_public: true,
  talking_points: [
    'Climate change is a critical issue',
    'We need immediate action',
    'Future generations depend on us'
  ],
  completion_percentage: 25,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockOfficials: Official[] = [
  {
    id: 'official-1',
    name: 'Senator Jane Smith',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.SENATE,
    party: 'D',
    state: 'CA',
    district: undefined,
    email: '<EMAIL>',
    phone: '(*************',
    photo_url: 'https://example.com/jane-smith.jpg',
    office_address: '123 Capitol Hill, Washington, DC 20510',
    website: 'https://smith.senate.gov',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'official-2',
    name: 'John Doe',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.HOUSE,
    party: 'R',
    state: 'CA',
    district: '12',
    email: '<EMAIL>',
    phone: '(*************',
    photo_url: undefined,
    office_address: '456 Capitol Hill, Washington, DC 20515',
    website: 'https://doe.house.gov',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

describe('ActionModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    campaign: mockCampaign,
    officials: mockOfficials,
    onSubmit: jest.fn(),
    isLoading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders correctly when opened', () => {
      render(<ActionModal {...defaultProps} />);
      
      // Check modal title
      expect(screen.getByText('Contact Your Representatives')).toBeInTheDocument();
      
      // Check campaign information
      expect(screen.getByText('Campaign: Test Campaign')).toBeInTheDocument();
      expect(screen.getByText('This is a test campaign for environmental protection')).toBeInTheDocument();
      
      // Check officials are displayed
      expect(screen.getByText('Senator Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('U.S. Senator (D)')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('U.S. Representative (R)')).toBeInTheDocument();
      
      // Check form fields
      expect(screen.getByTestId('action-modal-email-input')).toBeInTheDocument();
      expect(screen.getByTestId('action-modal-message-textarea')).toBeInTheDocument();
      
      // Check buttons
      expect(screen.getByTestId('action-modal-cancel-button')).toBeInTheDocument();
      expect(screen.getByTestId('action-modal-send-button')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<ActionModal {...defaultProps} isOpen={false} />);
      
      expect(screen.queryByText('Contact Your Representatives')).not.toBeInTheDocument();
    });

    it('displays default message with campaign call to action and talking points', () => {
      render(<ActionModal {...defaultProps} />);
      
      const messageTextarea = screen.getByTestId('action-modal-message-textarea') as HTMLTextAreaElement;
      
      expect(messageTextarea.value).toContain('Please support environmental protection legislation.');
      expect(messageTextarea.value).toContain('1. Climate change is a critical issue');
      expect(messageTextarea.value).toContain('2. We need immediate action');
      expect(messageTextarea.value).toContain('3. Future generations depend on us');
      expect(messageTextarea.value).toContain('I urge you to take action on this matter');
    });

    it('displays official photos when available', () => {
      render(<ActionModal {...defaultProps} />);
      
      const janeSmithPhoto = screen.getByAltText('Senator Jane Smith');
      expect(janeSmithPhoto).toBeInTheDocument();
      expect(janeSmithPhoto).toHaveAttribute('src', 'https://example.com/jane-smith.jpg');
    });

    it('displays initials when photo is not available', () => {
      render(<ActionModal {...defaultProps} />);
      
      // John Doe doesn't have a photo, so should show initials
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('validates email input correctly', async () => {
      const user = userEvent.setup();
      render(<ActionModal {...defaultProps} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      // Initially send button should be disabled (no email)
      expect(sendButton).toBeDisabled();
      
      // Enter invalid email
      await user.type(emailInput, 'invalid-email');
      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
      });
      expect(sendButton).toBeDisabled();
      
      // Clear and enter valid email
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await waitFor(() => {
        expect(screen.queryByText('Please enter a valid email address')).not.toBeInTheDocument();
      });
      expect(sendButton).not.toBeDisabled();
    });

    it('validates message input correctly', async () => {
      const user = userEvent.setup();
      render(<ActionModal {...defaultProps} />);
      
      const messageTextarea = screen.getByTestId('action-modal-message-textarea');
      const emailInput = screen.getByTestId('action-modal-email-input');
      
      // Enter valid email first
      await user.type(emailInput, '<EMAIL>');
      
      // Clear message (should be invalid)
      await user.clear(messageTextarea);
      await waitFor(() => {
        expect(screen.getByText('Message is required')).toBeInTheDocument();
      });
      
      // Enter short message
      await user.type(messageTextarea, 'Short');
      await waitFor(() => {
        expect(screen.getByText('Message must be at least 10 characters long')).toBeInTheDocument();
      });
      
      // Enter valid message
      await user.clear(messageTextarea);
      await user.type(messageTextarea, 'This is a valid message that is long enough');
      await waitFor(() => {
        expect(screen.queryByText('Message must be at least 10 characters long')).not.toBeInTheDocument();
      });
    });

    it('requires both email and message to enable send button', async () => {
      const user = userEvent.setup();
      render(<ActionModal {...defaultProps} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const messageTextarea = screen.getByTestId('action-modal-message-textarea');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      // Initially disabled
      expect(sendButton).toBeDisabled();
      
      // Add email only
      await user.type(emailInput, '<EMAIL>');
      // Message has default content, so button should be enabled
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled();
      });
      
      // Clear message
      await user.clear(messageTextarea);
      await waitFor(() => {
        expect(sendButton).toBeDisabled();
      });
      
      // Add message back
      await user.type(messageTextarea, 'This is a valid message');
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled();
      });
    });
  });

  describe('Form Submission', () => {
    it('calls onSubmit with correct data when form is submitted', async () => {
      const user = userEvent.setup();
      const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
      
      render(<ActionModal {...defaultProps} onSubmit={mockOnSubmit} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const messageTextarea = screen.getByTestId('action-modal-message-textarea');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      // Fill form
      await user.type(emailInput, '<EMAIL>');
      await user.clear(messageTextarea);
      await user.type(messageTextarea, 'This is my test message to representatives');
      
      // Submit form
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          email: '<EMAIL>',
          message: 'This is my test message to representatives',
          action_types: ['EMAIL']
        });
      });
    });

    it('closes modal after successful submission', async () => {
      const user = userEvent.setup();
      const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
      const mockOnClose = jest.fn();
      
      render(<ActionModal {...defaultProps} onSubmit={mockOnSubmit} onClose={mockOnClose} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      // Fill form and submit
      await user.type(emailInput, '<EMAIL>');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('handles submission errors gracefully', async () => {
      const user = userEvent.setup();
      const mockOnSubmit = jest.fn().mockRejectedValue(new Error('Submission failed'));
      const mockOnClose = jest.fn();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      render(<ActionModal {...defaultProps} onSubmit={mockOnSubmit} onClose={mockOnClose} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      // Fill form and submit
      await user.type(emailInput, '<EMAIL>');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to submit action:', expect.any(Error));
      });
      
      // Modal should not close on error
      expect(mockOnClose).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Loading State', () => {
    it('disables form elements when loading', () => {
      render(<ActionModal {...defaultProps} isLoading={true} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input');
      const messageTextarea = screen.getByTestId('action-modal-message-textarea');
      const cancelButton = screen.getByTestId('action-modal-cancel-button');
      const sendButton = screen.getByTestId('action-modal-send-button');
      
      expect(emailInput).toBeDisabled();
      expect(messageTextarea).toBeDisabled();
      expect(cancelButton).toBeDisabled();
      expect(sendButton).toBeDisabled();
    });

    it('shows loading text and spinner when loading', () => {
      render(<ActionModal {...defaultProps} isLoading={true} />);
      
      expect(screen.getByText('Sending...')).toBeInTheDocument();
      // Check for spinner (SVG element)
      expect(screen.getByRole('button', { name: /sending/i })).toBeInTheDocument();
    });
  });

  describe('Modal Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnClose = jest.fn();
      
      render(<ActionModal {...defaultProps} onClose={mockOnClose} />);
      
      const closeButton = screen.getByTestId('action-modal-close-button');
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('calls onClose when cancel button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnClose = jest.fn();
      
      render(<ActionModal {...defaultProps} onClose={mockOnClose} />);
      
      const cancelButton = screen.getByTestId('action-modal-cancel-button');
      await user.click(cancelButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('resets form when modal is closed', async () => {
      const user = userEvent.setup();
      const mockOnClose = jest.fn();
      
      render(<ActionModal {...defaultProps} onClose={mockOnClose} />);
      
      const emailInput = screen.getByTestId('action-modal-email-input') as HTMLInputElement;
      
      // Fill email
      await user.type(emailInput, '<EMAIL>');
      expect(emailInput.value).toBe('<EMAIL>');
      
      // Close modal
      const closeButton = screen.getByTestId('action-modal-close-button');
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });
  });
});
