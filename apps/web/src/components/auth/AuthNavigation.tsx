'use client';

import React from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import LoginButton from './LoginButton';
import UserProfile from './UserProfile';
import { useAuthToken } from '../../hooks/useAuthToken';

const AuthNavigation: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth0();
  
  // This hook ensures tokens are synced with the API client
  useAuthToken();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="text-sm text-gray-600">Loading...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      {isAuthenticated ? (
        <UserProfile />
      ) : (
        <LoginButton />
      )}
    </div>
  );
};

export default AuthNavigation;
