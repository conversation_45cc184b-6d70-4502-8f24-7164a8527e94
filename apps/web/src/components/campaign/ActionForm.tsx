'use client';

// ActionForm component - handles zip code input and action submission
import React, { useState } from 'react';
import toast from 'react-hot-toast';
import { Campaign, Official } from '../../types';
import { ActionModal, ActionFormData } from '../shared';
import { officialApi } from '../../services/apiClient';
import { createAction } from '../../services/actionService';

interface ActionFormProps {
  campaign: Campaign;
  onSubmit?: (zipCode: string, message?: string) => void;
  isLoading?: boolean;
}

const ActionForm: React.FC<ActionFormProps> = ({ 
  campaign, 
  onSubmit, 
  isLoading = false 
}) => {
  const [zipCode, setZipCode] = useState('');
  const [customMessage, setCustomMessage] = useState('');
  const [useCustomMessage, setUseCustomMessage] = useState(false);
  const [errors, setErrors] = useState<{ zipCode?: string; message?: string }>({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [officials, setOfficials] = useState<Official[]>([]);
  const [isLoadingOfficials, setIsLoadingOfficials] = useState(false);
  const [isSubmittingAction, setIsSubmittingAction] = useState(false);

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset errors
    setErrors({});
    
    // Validate zip code
    if (!zipCode.trim()) {
      setErrors({ zipCode: 'Zip code is required' });
      return;
    }
    
    if (!validateZipCode(zipCode.trim())) {
      setErrors({ zipCode: 'Please enter a valid 5-digit zip code' });
      return;
    }

    // Validate custom message if enabled
    if (useCustomMessage && customMessage.trim().length < 10) {
      setErrors({ message: 'Custom message must be at least 10 characters' });
      return;
    }

    try {
      setIsLoadingOfficials(true);
      
      // Fetch officials for the zip code
      const fetchedOfficials = await officialApi.getOfficialsByZip(zipCode.trim());
      
      if (fetchedOfficials.length === 0) {
        toast.error('No representatives found for this zip code. Please check and try again.');
        return;
      }
      
      setOfficials(fetchedOfficials);
      setIsModalOpen(true);
      
      // Call the legacy onSubmit if provided (for backward compatibility)
      if (onSubmit) {
        const message = useCustomMessage ? customMessage.trim() : undefined;
        onSubmit(zipCode.trim(), message);
      }
      
    } catch (error) {
      console.error('Failed to fetch officials:', error);
      toast.error('Failed to find your representatives. Please try again.');
    } finally {
      setIsLoadingOfficials(false);
    }
  };

  const handleZipCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 5) {
      setZipCode(value);
      if (errors.zipCode) {
        setErrors({ ...errors, zipCode: undefined });
      }
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCustomMessage(e.target.value);
    if (errors.message) {
      setErrors({ ...errors, message: undefined });
    }
  };

  const handleModalSubmit = async (data: ActionFormData) => {
    try {
      setIsSubmittingAction(true);
      
      const result = await createAction({
        formData: data,
        campaign,
        officials,
        userZipCode: zipCode.trim()
      });
      
      if (result.success) {
        const successCount = result.actions.length;
        const totalCount = officials.length;
        
        if (successCount === totalCount) {
          toast.success(`Successfully sent your message to ${successCount} representative${successCount > 1 ? 's' : ''}!`);
        } else {
          toast.success(`Sent your message to ${successCount} of ${totalCount} representatives.`);
          if (result.errors) {
            result.errors.forEach(error => toast.error(error));
          }
        }
        
        // Reset form
        setZipCode('');
        setCustomMessage('');
        setUseCustomMessage(false);
        setErrors({});
        
      } else {
        toast.error('Failed to send your message. Please try again.');
      }
      
    } catch (error) {
      console.error('Failed to submit action:', error);
      toast.error('Failed to send your message. Please try again.');
    } finally {
      setIsSubmittingAction(false);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setOfficials([]);
  };

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Take Action on This Campaign
      </h3>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Zip Code Input */}
        <div>
          <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
            Your Zip Code
          </label>
          <input
            type="text"
            id="zipCode"
            value={zipCode}
            onChange={handleZipCodeChange}
            placeholder="12345"
            className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
              errors.zipCode ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
          {errors.zipCode && (
            <p className="mt-1 text-sm text-red-600">{errors.zipCode}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            We&apos;ll find your representatives and send them your message
          </p>
        </div>

        {/* Talking Points */}
        {campaign.talking_points && campaign.talking_points.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Key Talking Points
            </h4>
            <ul className="space-y-1">
              {campaign.talking_points.map((point, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  {point}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Custom Message Option */}
        <div>
          <div className="flex items-center">
            <input
              id="useCustomMessage"
              type="checkbox"
              checked={useCustomMessage}
              onChange={(e) => setUseCustomMessage(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <label htmlFor="useCustomMessage" className="ml-2 block text-sm text-gray-700">
              Add a personal message (optional)
            </label>
          </div>
          
          {useCustomMessage && (
            <div className="mt-2">
              <textarea
                value={customMessage}
                onChange={handleMessageChange}
                rows={4}
                placeholder="Add your personal thoughts about this issue..."
                className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  errors.message ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
              {errors.message && (
                <p className="mt-1 text-sm text-red-600">{errors.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Personal messages are more effective than form letters
              </p>
            </div>
          )}
        </div>

        {/* Geographic Scope Info */}
        {campaign.geographic_scope && campaign.geographic_scope.length > 0 && (
          <div className="bg-blue-50 p-3 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-1">
              Campaign Focus Areas
            </h4>
            <div className="flex flex-wrap gap-1">
              {campaign.geographic_scope.map((area, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700"
                >
                  {area}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading || isLoadingOfficials || !zipCode.trim()}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
            isLoading || isLoadingOfficials || !zipCode.trim()
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {isLoading || isLoadingOfficials ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isLoadingOfficials ? 'Finding Representatives...' : 'Sending...'}
            </div>
          ) : (
            'Take Action Now'
          )}
        </button>

        {/* Privacy Notice */}
        <p className="text-xs text-gray-500 text-center">
          Your information will only be used to contact your representatives about this issue. 
          We respect your privacy and will never share your data.
        </p>
      </form>

      {/* Action Modal */}
      <ActionModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        campaign={campaign}
        officials={officials}
        onSubmit={handleModalSubmit}
        isLoading={isSubmittingAction}
      />
    </div>
  );
};

export default ActionForm;
