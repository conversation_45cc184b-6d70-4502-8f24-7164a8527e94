'use client';

import { useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import { setAuthToken } from '../services/apiClient';

/**
 * Hook to manage Auth0 tokens and sync them with the API client
 */
export const useAuthToken = () => {
  const { getAccessTokenSilently, isAuthenticated, isLoading } = useAuth0();

  useEffect(() => {
    const updateToken = async () => {
      if (isAuthenticated && !isLoading) {
        try {
          const token = await getAccessTokenSilently({
            authorizationParams: {
              audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
              scope: 'openid profile email'
            }
          });
          setAuthToken(token);
        } catch (error) {
          console.error('Failed to get access token:', error);
          setAuthToken(null);
        }
      } else {
        setAuthToken(null);
      }
    };

    updateToken();
  }, [isAuthenticated, isLoading, getAccessTokenSilently]);

  return {
    isAuthenticated,
    isLoading
  };
};
