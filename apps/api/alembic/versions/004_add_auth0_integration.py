"""Add Auth0 integration to User model

Revision ID: 004_add_auth0_integration
Revises: 003_add_bill_status_pipeline
Create Date: 2025-01-21 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004_add_auth0_integration'
down_revision = '003_add_bill_status_pipeline'
branch_labels = None
depends_on = None


def upgrade():
    # Add Auth0 integration columns to users table
    op.add_column('users', sa.<PERSON>umn('auth0_user_id', sa.String(), nullable=True))
    op.add_column('users', sa.Column('name', sa.String(), nullable=True))
    op.add_column('users', sa.Column('picture_url', sa.String(), nullable=True))
    op.add_column('users', sa.Column('email_verified', sa.<PERSON>an(), nullable=False, server_default='false'))
    
    # Create index on auth0_user_id for fast lookups
    op.create_index('ix_users_auth0_user_id', 'users', ['auth0_user_id'], unique=True)
    
    # Make legacy fields nullable since we're using Auth0
    op.alter_column('users', 'hashed_password', nullable=True)
    op.alter_column('users', 'first_name', nullable=True)
    op.alter_column('users', 'last_name', nullable=True)
    
    # Note: In production, you would need to populate auth0_user_id for existing users
    # or handle the migration of existing users to Auth0


def downgrade():
    # Remove Auth0 integration
    op.drop_index('ix_users_auth0_user_id', table_name='users')
    op.drop_column('users', 'email_verified')
    op.drop_column('users', 'picture_url')
    op.drop_column('users', 'name')
    op.drop_column('users', 'auth0_user_id')
    
    # Restore legacy field constraints
    op.alter_column('users', 'hashed_password', nullable=False)
    op.alter_column('users', 'first_name', nullable=False)
    op.alter_column('users', 'last_name', nullable=False)
