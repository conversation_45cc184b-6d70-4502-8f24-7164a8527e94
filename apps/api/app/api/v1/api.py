# app/api/v1/api.py
from fastapi import APIRouter
from app.api.v1.endpoints import health, officials, bills, campaigns, actions
# AI endpoints removed - AI functionality moved to background tasks only

api_router = APIRouter()

# Include all endpoint routers (AI removed to keep API lightweight)
api_router.include_router(health.router, tags=["health"])
api_router.include_router(officials.router, prefix="/officials", tags=["officials"])
api_router.include_router(bills.router, prefix="/bills", tags=["bills"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(actions.router, prefix="/actions", tags=["actions"])
# api_router.include_router(ai.router, prefix="/ai", tags=["ai"])  # Removed