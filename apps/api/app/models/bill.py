# app/models/bill.py
from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, In<PERSON><PERSON>, Enum as SQL<PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.db.base_class import Base
from app.db.types import get_json_type, get_uuid_type
import enum

class BillStatus(str, enum.Enum):
    DRAFT = "draft"
    INTRODUCED = "introduced"
    COMMITTEE = "committee"
    FLOOR = "floor"
    PASSED = "passed"
    SIGNED = "signed"
    VETOED = "vetoed"
    FAILED = "failed"

class BillType(str, enum.Enum):
    HOUSE_BILL = "house_bill"
    SENATE_BILL = "senate_bill"
    HOUSE_RESOLUTION = "house_resolution"
    SENATE_RESOLUTION = "senate_resolution"
    JOINT_RESOLUTION = "joint_resolution"
    CONCURRENT_RESOLUTION = "concurrent_resolution"

class Bill(Base):
    __tablename__ = "bills"
    
    # Basic bill information
    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    bill_number = Column(String, nullable=False, index=True)
    
    # Bill classification
    bill_type = Column(SQLEnum(BillType), nullable=False)
    status = Column(SQLEnum(BillStatus), nullable=False, default=BillStatus.INTRODUCED)
    
    # Legislative session information
    session_year = Column(Integer, nullable=False)
    chamber = Column(String, nullable=False)  # "house" or "senate"
    state = Column(String, nullable=False, default="federal")  # state abbreviation or "federal"
    
    # Bill content
    full_text = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    ai_summary = Column(Text, nullable=True)  # AI-generated summary
    
    # External references
    openstates_id = Column(String, nullable=True, unique=True, index=True)
    congress_gov_id = Column(String, nullable=True, unique=True, index=True)
    
    # URLs and links
    source_url = Column(String, nullable=True)
    text_url = Column(String, nullable=True)
    
    # Dates
    introduced_date = Column(DateTime, nullable=True)
    last_action_date = Column(DateTime, nullable=True)
    
    # Sponsorship information
    sponsor_name = Column(String, nullable=True)
    sponsor_party = Column(String, nullable=True)
    sponsor_state = Column(String, nullable=True)
    
    # Co-sponsors and vote information
    cosponsors = Column(get_json_type(), nullable=True)  # Array of cosponsor information
    vote_history = Column(get_json_type(), nullable=True)  # Array of vote records
    
    # Tracking and engagement
    is_featured = Column(Boolean, default=False, nullable=False)
    priority_score = Column(Integer, default=0, nullable=False)
    
    # Tags and categories
    tags = Column(get_json_type(), nullable=True)  # Array of tags
    categories = Column(get_json_type(), nullable=True)  # Array of categories
    
    # Metadata
    bill_metadata = Column(get_json_type(), nullable=True)  # Additional metadata
    
    # Relationships
    campaigns = relationship("Campaign", back_populates="bill", cascade="all, delete-orphan")
    status_history = relationship("BillStatusPipeline", back_populates="bill", cascade="all, delete-orphan", order_by="BillStatusPipeline.detected_at.desc()")
    
    def __repr__(self):
        return f"<Bill(number='{self.bill_number}', title='{self.title[:50]}...')>"
    
    @property
    def full_bill_id(self) -> str:
        return f"{self.state.upper()}-{self.bill_number}-{self.session_year}"


class BillStatusPipeline(Base):
    """
    Model for tracking bill status changes over time.

    This table maintains a history of all status changes for bills,
    enabling us to track the legislative progress and notify users
    when significant changes occur.
    """
    __tablename__ = "bill_status_pipeline"

    # Foreign key to the bill
    bill_id = Column(get_uuid_type(), ForeignKey("bills.id"), nullable=False, index=True)

    # Status information
    previous_status = Column(SQLEnum(BillStatus), nullable=True)  # Previous status (null for first entry)
    current_status = Column(SQLEnum(BillStatus), nullable=False, index=True)

    # Change metadata
    status_changed_at = Column(DateTime, nullable=False)  # When the status actually changed
    detected_at = Column(DateTime, nullable=False)  # When our system detected the change

    # External API data
    external_data = Column(get_json_type(), nullable=True)  # Raw data from OpenStates API
    vote_details = Column(get_json_type(), nullable=True)  # Specific vote information if available

    # Processing flags
    notification_sent = Column(Boolean, default=False, nullable=False)  # Whether users were notified
    is_significant_change = Column(Boolean, default=False, nullable=False)  # Whether this warrants notification

    # Additional context
    notes = Column(Text, nullable=True)  # Any additional notes about the status change

    # Relationships
    bill = relationship("Bill", back_populates="status_history")

    def __repr__(self):
        return f"<BillStatusPipeline(bill_id='{self.bill_id}', {self.previous_status} -> {self.current_status})>"

    @property
    def is_final_status(self) -> bool:
        """Check if this status represents a final outcome"""
        return self.current_status in [BillStatus.PASSED, BillStatus.SIGNED, BillStatus.VETOED, BillStatus.FAILED]