# app/tasks.py
"""
Background tasks for the ModernAction API.

This module contains all background task functions that are executed
asynchronously to avoid blocking API responses.
"""

import logging
import time
from functools import lru_cache
from typing import Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from app.models.bill import Bill

# AI functionality moved here for background processing only
try:
    from transformers import pipeline
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

logger = logging.getLogger(__name__)

# AI functions moved from app.services.ai to keep them in background tasks only

@lru_cache(maxsize=None)
def get_summarizer():
    """
    Get the summarization pipeline as a singleton.
    This function will only run once due to @lru_cache.
    """
    if not AI_AVAILABLE:
        raise RuntimeError("Transformers library not available for AI processing")
    
    logger.info("Loading summarization model...")
    try:
        summarizer = pipeline(
            "summarization",
            model="t5-small",
            tokenizer="t5-small", 
            framework="pt",
            device=-1  # Use CPU
        )
        logger.info("Successfully loaded summarization model: t5-small")
        return summarizer
    except Exception as e:
        logger.error(f"Failed to load summarization model: {e}")
        raise RuntimeError(f"Could not initialize summarization model: {e}")

@lru_cache(maxsize=None)
def get_text_generator():
    """Get the text generation pipeline as a singleton."""
    if not AI_AVAILABLE:
        raise RuntimeError("Transformers library not available for AI processing")
        
    logger.info("Loading text generation model...")
    try:
        generator = pipeline(
            "text2text-generation",
            model="t5-small",
            tokenizer="t5-small",
            framework="pt",
            device=-1  # Use CPU
        )
        logger.info("Successfully loaded text generation model: t5-small")
        return generator
    except Exception as e:
        logger.error(f"Failed to load text generation model: {e}")
        raise RuntimeError(f"Could not initialize text generation model: {e}")

def summarize_text(text: str, max_length: int = 150, min_length: int = 30) -> str:
    """Summarize long text into a shorter version."""
    if not text or not text.strip():
        raise ValueError("Text to summarize cannot be empty")
    
    text = text.strip()
    
    if len(text) < min_length:
        logger.warning(f"Text too short for summarization: {len(text)} characters")
        return text
    
    MAX_INPUT_LENGTH = 1024
    if len(text) > MAX_INPUT_LENGTH:
        logger.info(f"Truncating text from {len(text)} to {MAX_INPUT_LENGTH} characters")
        text = text[:MAX_INPUT_LENGTH]
    
    try:
        summarizer = get_summarizer()
        
        logger.info("Generating summary...")
        summary_result = summarizer(
            text,
            max_length=max_length,
            min_length=min_length,
            do_sample=False,
            truncation=True
        )
        
        summary = summary_result[0]['summary_text']
        logger.info(f"Generated summary: {len(summary)} characters")
        return summary.strip()
        
    except Exception as e:
        logger.error(f"Summarization failed: {e}")
        raise RuntimeError(f"Failed to generate summary: {e}")

def summarize_bill(bill_text: str, title: str = "") -> str:
    """Specialized function for summarizing bill text with context."""
    if not bill_text:
        return "No bill text available for summarization."
    
    if title:
        contextualized_text = f"Bill Title: {title}\n\nBill Text: {bill_text}"
    else:
        contextualized_text = bill_text
    
    try:
        summary = summarize_text(
            contextualized_text,
            max_length=200,
            min_length=50
        )
        
        summary = summary.replace("SEC.", "Section")
        summary = summary.replace("SECTION", "Section")
        
        return summary
        
    except Exception as e:
        logger.error(f"Bill summarization failed: {e}")
        return f"Summary generation failed: {str(e)}"

def personalize_message(raw_text: str, context: str, tone: str = "professional") -> Dict[str, Any]:
    """Personalize a user's raw message using AI."""
    start_time = time.time()

    if not raw_text or not raw_text.strip():
        raise ValueError("Raw text cannot be empty")

    if not context or not context.strip():
        raise ValueError("Context cannot be empty")

    raw_text = raw_text.strip()
    context = context.strip()

    try:
        prompt = _create_personalization_prompt(raw_text, context, tone)
        generator = get_text_generator()

        logger.info("Generating personalized message...")
        result = generator(
            prompt,
            max_length=300,
            min_length=50,
            do_sample=True,
            temperature=0.7,
            truncation=True
        )

        personalized_text = result[0]['generated_text'].strip()
        personalized_text = _post_process_personalized_message(personalized_text)

        processing_time = (time.time() - start_time) * 1000

        logger.info(f"Generated personalized message: {len(personalized_text)} characters in {processing_time:.1f}ms")

        return {
            "personalized_message": personalized_text,
            "original_length": len(raw_text),
            "personalized_length": len(personalized_text),
            "processing_time_ms": processing_time
        }

    except Exception as e:
        logger.error(f"Message personalization failed: {e}")
        raise RuntimeError(f"Failed to personalize message: {e}")

def _create_personalization_prompt(raw_text: str, context: str, tone: str) -> str:
    """Create a structured prompt for message personalization."""
    tone_instructions = {
        "professional": "Write in a professional, respectful tone suitable for official correspondence.",
        "passionate": "Write with passion and urgency while remaining respectful and constructive.",
        "formal": "Write in a formal, diplomatic tone appropriate for government officials.",
        "personal": "Write in a personal, heartfelt tone that emphasizes individual impact.",
        "urgent": "Write with a sense of urgency and importance while remaining professional."
    }

    tone_instruction = tone_instructions.get(tone, tone_instructions["professional"])

    prompt = f"""Transform the following personal message into a persuasive advocacy letter.

Context: {context}

Personal Message: {raw_text}

Instructions: {tone_instruction} Make the message more structured and persuasive while preserving the personal elements. Include a clear call to action.

Transformed Message:"""

    return prompt

def _post_process_personalized_message(text: str) -> str:
    """Post-process the AI-generated message to ensure quality."""
    text = text.replace("Transformed Message:", "").strip()

    if not text.endswith(('.', '!', '?')):
        text += '.'

    if text and text[0].islower():
        text = text[0].upper() + text[1:]

    return text

logger = logging.getLogger(__name__)

def task_generate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to generate AI summary for a bill.
    
    This function runs in the background after a bill is created via the API.
    It fetches the bill, generates an AI summary, and updates the database.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Generate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill already has an AI summary
        if bill.ai_summary and bill.ai_summary.strip():
            logger.info(f"Bill {bill_id} already has AI summary, skipping")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot generate summary")
            return
        
        # Generate AI summary
        logger.info(f"Generating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the generated summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully generated and saved AI summary for bill {bill_id}")
        logger.debug(f"Generated summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_regenerate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to regenerate AI summary for an existing bill.
    
    This function is similar to task_generate_summary_for_bill but will
    overwrite existing summaries. Useful for updating summaries when
    the AI model or bill text changes.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Regenerate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot regenerate summary")
            return
        
        # Generate AI summary (overwrite existing)
        logger.info(f"Regenerating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the new summary
        old_summary = bill.ai_summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully regenerated AI summary for bill {bill_id}")
        logger.debug(f"Old summary: {old_summary[:50] if old_summary else 'None'}...")
        logger.debug(f"New summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_bulk_generate_summaries(bill_ids: list[UUID], db: Session) -> None:
    """
    Background task to generate AI summaries for multiple bills.
    
    This function processes multiple bills in sequence. It's useful for
    bulk operations without blocking the API.
    
    Args:
        bill_ids: List of bill UUIDs to process
        db: Database session
    """
    logger.info(f"Starting bulk summary generation for {len(bill_ids)} bills")
    
    successful = 0
    failed = 0
    
    for bill_id in bill_ids:
        try:
            task_generate_summary_for_bill(bill_id, db)
            successful += 1
        except Exception as e:
            logger.error(f"Failed to process bill {bill_id} in bulk operation: {e}")
            failed += 1
    
    logger.info(f"Bulk summary generation complete: {successful} successful, {failed} failed")

def task_process_action(action_id: UUID, db: Session) -> None:
    """
    Background task to process actions (email and/or Twitter).

    This function runs in the background after an action is created via the API.
    It fetches the action details and processes all requested action types.

    Args:
        action_id: UUID of the action to process
        db: Database session
    """
    from app.models.action import Action, ActionStatus
    from app.services.action import ActionService
    
    try:
        logger.info(f"Starting background task: Send action email for action {action_id}")
        
        # Fetch the action from the database
        action = db.query(Action).filter(Action.id == action_id).first()
        
        if not action:
            logger.error(f"Action {action_id} not found in database")
            return
        
        # Check if action is in pending status
        if action.status != ActionStatus.PENDING:
            logger.info(f"Action {action_id} is not in pending status, skipping")
            return

        # Get action types from the action record
        action_types = action.action_types or ["EMAIL"]  # Default to email if not specified
        logger.info(f"Processing action {action_id} with types: {action_types}")

        # Initialize service for status updates
        service = ActionService(db)
        from app.schemas.action import ActionUpdate

        # Track results for each action type
        results = {
            "email": {"attempted": False, "success": False, "error": None},
            "twitter": {"attempted": False, "success": False, "error": None}
        }
        
        # Process EMAIL action type
        if "EMAIL" in action_types:
            results["email"]["attempted"] = True

            if not action.contact_email:
                logger.warning(f"Action {action_id} has no contact email, cannot send email")
                results["email"]["error"] = "No contact email available"
            else:
                try:
                    logger.info(f"Sending email for action {action_id} to {action.contact_email}")
                    logger.info(f"Subject: {action.subject}")
                    logger.info(f"Message preview: {action.message[:100]}...")

                    # Import email service here to avoid circular imports
                    from app.services.email import EmailService

                    email_service = EmailService()

                    # Send the action email
                    email_result = email_service.send_action_email(
                        to_address=action.contact_email,
                        user_name=action.user_name,
                        user_email=action.user_email,
                        user_address=action.user_address,
                        user_zip_code=action.user_zip_code,
                        subject=action.subject,
                        message=action.message,
                        official_name=action.official.name
                    )

                    if email_result['success']:
                        results["email"]["success"] = True
                        logger.info(f"Successfully sent email for action {action_id}. Message ID: {email_result.get('message_id')}")
                    else:
                        results["email"]["error"] = email_result.get('error_message', 'Email sending failed')
                        logger.error(f"Failed to send email for action {action_id}: {results['email']['error']}")

                except Exception as e:
                    results["email"]["error"] = str(e)
                    logger.error(f"Exception while sending email for action {action_id}: {e}")

        # Process TWITTER action type
        if "TWITTER" in action_types:
            results["twitter"]["attempted"] = True

            if not action.official.twitter_handle:
                logger.warning(f"Action {action_id} official has no Twitter handle, cannot send tweet")
                results["twitter"]["error"] = "Official has no Twitter handle"
            else:
                try:
                    logger.info(f"Posting tweet for action {action_id} to @{action.official.twitter_handle}")

                    # Import Twitter service here to avoid circular imports
                    from app.services.twitter import TwitterService

                    twitter_service = TwitterService()

                    if not twitter_service.is_available():
                        results["twitter"]["error"] = "Twitter service not configured"
                        logger.warning(f"Twitter service not available for action {action_id}")
                    else:
                        # Construct tweet message (shorter than email)
                        tweet_message = action.message[:200]  # Truncate to fit Twitter limits
                        if len(action.message) > 200:
                            tweet_message = tweet_message.rstrip() + "..."

                        # Post the tweet
                        tweet_result = twitter_service.post_action_tweet(
                            message=tweet_message,
                            official_twitter_handle=action.official.twitter_handle,
                            campaign_hashtag=getattr(action.campaign, 'hashtag', None),
                            user_name=action.user_name
                        )

                        if tweet_result.get('success'):
                            results["twitter"]["success"] = True
                            logger.info(f"Successfully posted tweet for action {action_id}. Tweet ID: {tweet_result.get('tweet_id')}")
                        else:
                            results["twitter"]["error"] = tweet_result.get('error_message', 'Tweet posting failed')
                            logger.error(f"Failed to post tweet for action {action_id}: {results['twitter']['error']}")

                except Exception as e:
                    results["twitter"]["error"] = str(e)
                    logger.error(f"Exception while posting tweet for action {action_id}: {e}")
        
        # Determine overall action status based on results
        attempted_count = sum(1 for r in results.values() if r["attempted"])
        success_count = sum(1 for r in results.values() if r["success"])

        if attempted_count == 0:
            # No action types were attempted
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message="No valid action types could be processed"
            ))
            logger.error(f"No action types could be processed for action {action_id}")
            return

        if success_count == attempted_count:
            # All attempted actions succeeded
            delivery_methods = []
            if results["email"]["success"]:
                delivery_methods.append("email")
            if results["twitter"]["success"]:
                delivery_methods.append("twitter")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.SENT,
                delivery_method=",".join(delivery_methods)
            ))
            logger.info(f"Successfully processed all action types for action {action_id}: {delivery_methods}")

        elif success_count > 0:
            # Some actions succeeded, some failed
            error_messages = []
            if results["email"]["attempted"] and not results["email"]["success"]:
                error_messages.append(f"Email: {results['email']['error']}")
            if results["twitter"]["attempted"] and not results["twitter"]["success"]:
                error_messages.append(f"Twitter: {results['twitter']['error']}")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.PARTIAL,
                error_message="; ".join(error_messages)
            ))
            logger.warning(f"Partial success for action {action_id}: {success_count}/{attempted_count} succeeded")

        else:
            # All attempted actions failed
            error_messages = []
            if results["email"]["attempted"]:
                error_messages.append(f"Email: {results['email']['error']}")
            if results["twitter"]["attempted"]:
                error_messages.append(f"Twitter: {results['twitter']['error']}")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message="; ".join(error_messages)
            ))
            logger.error(f"All action types failed for action {action_id}")
        
        logger.info(f"Successfully processed action {action_id}")
        
    except Exception as e:
        logger.error(f"Background task failed for action {action_id}: {str(e)}")
        
        # Update action status to failed
        try:
            service = ActionService(db)
            from app.schemas.action import ActionUpdate
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message=str(e)
            ))
        except Exception as update_error:
            logger.error(f"Failed to update action {action_id} status: {str(update_error)}")
        
        # Don't re-raise - background tasks should handle errors gracefully