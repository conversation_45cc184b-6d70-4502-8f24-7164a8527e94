# app/services/bill_status_update.py
"""
Service for updating bill statuses from external APIs.

This service handles fetching bill status updates from OpenStates API,
comparing with current database status, and creating status change records.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import requests
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.config import settings
from app.models.bill import Bill, BillStatus, BillStatusPipeline
from app.db.database import SessionLocal

logger = logging.getLogger(__name__)


class BillStatusUpdateService:
    """Service for updating bill statuses from external APIs"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_active_bills(self) -> List[Bill]:
        """
        Get all bills that are in an active status (not yet passed, failed, signed, or vetoed).
        
        Returns:
            List of Bill objects that need status checking
        """
        active_statuses = [
            BillStatus.DRAFT,
            BillStatus.INTRODUCED,
            BillStatus.COMMITTEE,
            BillStatus.FLOOR
        ]
        
        bills = (
            self.db.query(Bill)
            .filter(Bill.status.in_(active_statuses))
            .filter(Bill.openstates_id.isnot(None))  # Only bills with OpenStates ID
            .all()
        )
        
        logger.info(f"Found {len(bills)} active bills to check for status updates")
        return bills
    
    def fetch_bill_status_from_openstates(self, openstates_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch current bill status from OpenStates API.
        
        Args:
            openstates_id: OpenStates bill ID
            
        Returns:
            Dict containing bill data or None if not found
        """
        if not settings.OPEN_STATES_API_KEY:
            logger.error("OPEN_STATES_API_KEY not configured")
            return None
        
        api_url = f"https://v3.openstates.org/bills/{openstates_id}"
        headers = {
            "X-API-KEY": settings.OPEN_STATES_API_KEY,
            "Accept": "application/json"
        }
        
        try:
            logger.debug(f"Fetching bill status from OpenStates API: {openstates_id}")
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"Successfully fetched bill status: {data.get('title', 'Unknown Title')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch bill from OpenStates API: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching bill: {e}")
            return None
    
    def map_openstates_status_to_bill_status(self, openstates_data: Dict[str, Any]) -> BillStatus:
        """
        Map OpenStates status to our BillStatus enum.
        
        Args:
            openstates_data: Raw data from OpenStates API
            
        Returns:
            BillStatus enum value
        """
        # Enhanced status mapping based on OpenStates actions
        actions = openstates_data.get('actions', [])
        latest_action = actions[-1] if actions else {}
        
        # Check for final statuses first
        if any('signed' in action.get('description', '').lower() for action in actions):
            return BillStatus.SIGNED
        if any('vetoed' in action.get('description', '').lower() for action in actions):
            return BillStatus.VETOED
        if any('passed' in action.get('description', '').lower() and 'final' in action.get('description', '').lower() for action in actions):
            return BillStatus.PASSED
        if any('failed' in action.get('description', '').lower() or 'defeated' in action.get('description', '').lower() for action in actions):
            return BillStatus.FAILED
        
        # Check for intermediate statuses
        latest_description = latest_action.get('description', '').lower()
        if 'floor' in latest_description or 'third reading' in latest_description:
            return BillStatus.FLOOR
        if 'committee' in latest_description:
            return BillStatus.COMMITTEE
        
        # Default to introduced if no specific status found
        return BillStatus.INTRODUCED
    
    def is_significant_status_change(self, old_status: BillStatus, new_status: BillStatus) -> bool:
        """
        Determine if a status change is significant enough to warrant user notification.
        
        Args:
            old_status: Previous bill status
            new_status: New bill status
            
        Returns:
            True if the change is significant
        """
        # Define status progression order
        status_order = {
            BillStatus.DRAFT: 0,
            BillStatus.INTRODUCED: 1,
            BillStatus.COMMITTEE: 2,
            BillStatus.FLOOR: 3,
            BillStatus.PASSED: 4,
            BillStatus.SIGNED: 5,
            BillStatus.VETOED: 5,
            BillStatus.FAILED: 5
        }
        
        old_order = status_order.get(old_status, 0)
        new_order = status_order.get(new_status, 0)
        
        # Significant if it's a progression or a final status
        return (new_order > old_order) or new_status in [BillStatus.PASSED, BillStatus.SIGNED, BillStatus.VETOED, BillStatus.FAILED]
    
    def create_status_change_record(
        self, 
        bill: Bill, 
        new_status: BillStatus, 
        external_data: Dict[str, Any],
        status_changed_at: datetime
    ) -> BillStatusPipeline:
        """
        Create a new status change record in the pipeline.
        
        Args:
            bill: The bill that changed status
            new_status: The new status
            external_data: Raw data from external API
            status_changed_at: When the status actually changed
            
        Returns:
            Created BillStatusPipeline record
        """
        previous_status = bill.status
        is_significant = self.is_significant_status_change(previous_status, new_status)
        
        # Extract vote details if available
        vote_details = None
        actions = external_data.get('actions', [])
        for action in reversed(actions):  # Check most recent actions first
            if any(keyword in action.get('description', '').lower() for keyword in ['vote', 'passed', 'failed']):
                vote_details = {
                    'action_description': action.get('description'),
                    'action_date': action.get('date'),
                    'organization': action.get('organization', {}).get('name'),
                    'result': action.get('result')
                }
                break
        
        status_record = BillStatusPipeline(
            bill_id=bill.id,
            previous_status=previous_status,
            current_status=new_status,
            status_changed_at=status_changed_at,
            detected_at=datetime.utcnow(),
            external_data=external_data,
            vote_details=vote_details,
            notification_sent=False,
            is_significant_change=is_significant,
            notes=f"Status updated from {previous_status} to {new_status}"
        )
        
        self.db.add(status_record)
        
        # Update the bill's current status
        bill.status = new_status
        bill.last_action_date = status_changed_at
        
        logger.info(f"Created status change record for bill {bill.id}: {previous_status} -> {new_status}")
        return status_record
    
    def update_bill_status(self, bill: Bill) -> Optional[BillStatusPipeline]:
        """
        Update a single bill's status by checking external API.
        
        Args:
            bill: Bill to update
            
        Returns:
            BillStatusPipeline record if status changed, None otherwise
        """
        if not bill.openstates_id:
            logger.warning(f"Bill {bill.id} has no OpenStates ID, skipping")
            return None
        
        # Fetch current status from API
        external_data = self.fetch_bill_status_from_openstates(bill.openstates_id)
        if not external_data:
            logger.warning(f"Could not fetch status for bill {bill.id} from OpenStates")
            return None
        
        # Determine new status
        new_status = self.map_openstates_status_to_bill_status(external_data)
        
        # Check if status has changed
        if new_status == bill.status:
            logger.debug(f"No status change for bill {bill.id}: still {bill.status}")
            return None
        
        # Determine when the status changed (use latest action date)
        actions = external_data.get('actions', [])
        status_changed_at = datetime.utcnow()  # Default to now
        if actions:
            latest_action_date = actions[-1].get('date')
            if latest_action_date:
                try:
                    status_changed_at = datetime.fromisoformat(latest_action_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    pass  # Use default
        
        # Create status change record
        status_record = self.create_status_change_record(
            bill=bill,
            new_status=new_status,
            external_data=external_data,
            status_changed_at=status_changed_at
        )
        
        return status_record
    
    def update_all_active_bills(self) -> Tuple[int, int]:
        """
        Update status for all active bills.
        
        Returns:
            Tuple of (total_checked, total_updated)
        """
        active_bills = self.get_active_bills()
        total_checked = len(active_bills)
        total_updated = 0
        
        for bill in active_bills:
            try:
                status_record = self.update_bill_status(bill)
                if status_record:
                    total_updated += 1
                    self.db.commit()
                    logger.info(f"Updated bill {bill.id}: {status_record.previous_status} -> {status_record.current_status}")
                
            except Exception as e:
                logger.error(f"Error updating bill {bill.id}: {e}")
                self.db.rollback()
                continue
        
        logger.info(f"Bill status update complete: {total_updated}/{total_checked} bills updated")
        return total_checked, total_updated
