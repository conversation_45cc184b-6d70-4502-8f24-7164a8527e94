__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis
.DS_Store
.vscode
.idea
*.swp
*.swo
*~

# Test files
tests/
test_*.py
*_test.py

# Documentation
docs/
*.md
README*

# Development files
.env
.env.*
poetry.lock
pyproject.toml

# Database
*.db
*.sqlite3

# Logs
logs/
*.log